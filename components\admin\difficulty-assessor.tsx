"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  TrendingUp, 
  Brain, 
  BookOpen, 
  Target, 
  Lightbulb,
  Loader2,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { toast } from "sonner"
import { motion } from "framer-motion"

interface Question {
  id: string
  type: string
  text: string
  options?: string[]
  correctAnswer: string
}

interface DifficultyFactors {
  conceptComplexity: 'low' | 'medium' | 'high'
  vocabularyLevel: 'basic' | 'intermediate' | 'advanced'
  cognitiveLoad: 'low' | 'medium' | 'high'
  prerequisiteKnowledge: 'minimal' | 'moderate' | 'extensive'
}

interface QuestionAssessment {
  id: string
  suggestedDifficulty: 'EASY' | 'MEDIUM' | 'HARD'
  confidence: number
  reasoning: string
  adjustmentSuggestions: string[]
}

interface DifficultyAssessment {
  overallDifficulty: 'EASY' | 'MEDIUM' | 'HARD'
  confidence: number
  reasoning: string
  factors: DifficultyFactors
  questions?: QuestionAssessment[]
  recommendations: string[]
}

interface DifficultyAssessorProps {
  content: string
  questions?: Question[]
  currentDifficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  onDifficultyChange?: (difficulty: 'EASY' | 'MEDIUM' | 'HARD') => void
}

const difficultyColors = {
  EASY: "bg-green-100 text-green-800 border-green-200",
  MEDIUM: "bg-yellow-100 text-yellow-800 border-yellow-200",
  HARD: "bg-red-100 text-red-800 border-red-200"
}

const factorColors = {
  low: "text-green-600",
  basic: "text-green-600",
  minimal: "text-green-600",
  medium: "text-yellow-600",
  intermediate: "text-yellow-600",
  moderate: "text-yellow-600",
  high: "text-red-600",
  advanced: "text-red-600",
  extensive: "text-red-600"
}

const factorIcons = {
  conceptComplexity: Brain,
  vocabularyLevel: BookOpen,
  cognitiveLoad: TrendingUp,
  prerequisiteKnowledge: Target
}

export function DifficultyAssessor({ 
  content, 
  questions = [], 
  currentDifficulty,
  onDifficultyChange 
}: DifficultyAssessorProps) {
  const [assessment, setAssessment] = useState<DifficultyAssessment | null>(null)
  const [loading, setLoading] = useState(false)

  const assessDifficulty = async () => {
    if (!content.trim()) {
      toast.error('Please provide content to assess difficulty')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/quizzes/assess-difficulty', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          content,
          questions: questions.length > 0 ? questions : undefined
        })
      })

      if (response.ok) {
        const data = await response.json()
        setAssessment(data.data)
        toast.success('Difficulty assessment completed')
      } else {
        throw new Error('Failed to assess difficulty')
      }
    } catch (error) {
      console.error('Error assessing difficulty:', error)
      toast.error('Failed to assess difficulty')
    } finally {
      setLoading(false)
    }
  }

  const applyDifficultySuggestion = () => {
    if (assessment && onDifficultyChange) {
      onDifficultyChange(assessment.overallDifficulty)
      toast.success(`Difficulty updated to ${assessment.overallDifficulty}`)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">AI Difficulty Assessment</h3>
          <p className="text-sm text-muted-foreground">
            Analyze and optimize quiz difficulty level
          </p>
        </div>
        <Button onClick={assessDifficulty} disabled={loading || !content.trim()}>
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Brain className="h-4 w-4 mr-2" />
          )}
          Assess Difficulty
        </Button>
      </div>

      {assessment && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Overall Assessment */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Difficulty Assessment
                  </CardTitle>
                  <CardDescription>
                    AI-powered analysis of content difficulty
                  </CardDescription>
                </div>
                <div className="text-right">
                  <Badge className={`text-lg px-3 py-1 ${difficultyColors[assessment.overallDifficulty]}`}>
                    {assessment.overallDifficulty}
                  </Badge>
                  <div className="text-sm text-muted-foreground mt-1">
                    {Math.round(assessment.confidence * 100)}% confidence
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Analysis</h4>
                  <p className="text-sm text-muted-foreground">{assessment.reasoning}</p>
                </div>
                
                <Progress value={assessment.confidence * 100} className="h-2" />
                
                {currentDifficulty && currentDifficulty !== assessment.overallDifficulty && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription className="flex items-center justify-between">
                      <span>
                        Current difficulty ({currentDifficulty}) differs from AI suggestion ({assessment.overallDifficulty})
                      </span>
                      <Button size="sm" onClick={applyDifficultySuggestion}>
                        Apply Suggestion
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Difficulty Factors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Difficulty Factors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(assessment.factors).map(([factor, level]) => {
                  const Icon = factorIcons[factor as keyof typeof factorIcons]
                  return (
                    <div key={factor} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span className="font-medium capitalize">
                          {factor.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                      </div>
                      <Badge className={factorColors[level as keyof typeof factorColors]}>
                        {level}
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Question-Level Assessment */}
          {assessment.questions && assessment.questions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Question Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {assessment.questions.map((question, index) => (
                    <div key={question.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">Question {index + 1}</span>
                        <div className="flex items-center gap-2">
                          <Badge className={difficultyColors[question.suggestedDifficulty]}>
                            {question.suggestedDifficulty}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {Math.round(question.confidence * 100)}%
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">{question.reasoning}</p>
                      
                      {question.adjustmentSuggestions.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium mb-2">Adjustment Suggestions:</h5>
                          <ul className="space-y-1">
                            {question.adjustmentSuggestions.map((suggestion, suggestionIndex) => (
                              <li key={suggestionIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                                <Lightbulb className="h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0" />
                                {suggestion}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {assessment.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {assessment.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      {recommendation}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </motion.div>
      )}
    </div>
  )
}
