import { generateText, generateObject, tool } from 'ai'
import { z } from 'zod'
import { getModelInstance, getBestModelForTask } from '@/lib/ai-providers'

// Schemas for structured outputs
const quizAnalysisSchema = z.object({
  topic: z.string(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  suggestedQuestionCount: z.number().min(5).max(50),
  learningObjectives: z.array(z.string()),
  keyTopics: z.array(z.string()),
  targetAudience: z.string(),
  estimatedDuration: z.number(),
  tags: z.array(z.string()),
  prerequisites: z.array(z.string())
})

const questionSchema = z.object({
  id: z.string(),
  type: z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING']),
  text: z.string(),
  options: z.array(z.string()).optional(),
  correctAnswer: z.string(),
  explanation: z.string(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  tags: z.array(z.string()),
  points: z.number().min(1).max(10),
  estimatedTime: z.number(), // in seconds
  bloomsLevel: z.enum(['remember', 'understand', 'apply', 'analyze', 'evaluate', 'create'])
})

const quizStructureSchema = z.object({
  title: z.string(),
  description: z.string(),
  instructions: z.string(),
  questions: z.array(questionSchema),
  metadata: z.object({
    totalPoints: z.number(),
    estimatedDuration: z.number(),
    difficultyDistribution: z.object({
      easy: z.number(),
      medium: z.number(),
      hard: z.number()
    }),
    bloomsDistribution: z.object({
      remember: z.number(),
      understand: z.number(),
      apply: z.number(),
      analyze: z.number(),
      evaluate: z.number(),
      create: z.number()
    })
  })
})

export class QuizCreationAgent {
  private orchestratorModel: any
  private contentAnalyzer: any
  private questionGenerator: any
  private qualityEvaluator: any

  constructor(options?: {
    orchestratorModel?: string
    contentAnalyzer?: string
    questionGenerator?: string
    qualityEvaluator?: string
    useOpenAIOnly?: boolean
  }) {
    try {
      // If useOpenAIOnly is true or no specific models provided, use OpenAI configuration
      if (options?.useOpenAIOnly !== false) {
        const { getOpenAIConfiguration } = require('@/lib/ai-providers')
        const openAIConfig = getOpenAIConfiguration()

        this.orchestratorModel = getModelInstance(
          options?.orchestratorModel || openAIConfig.orchestrator.id
        )

        this.contentAnalyzer = getModelInstance(
          options?.contentAnalyzer || openAIConfig.contentAnalyzer.id
        )

        this.questionGenerator = getModelInstance(
          options?.questionGenerator || openAIConfig.questionGenerator.id
        )

        this.qualityEvaluator = getModelInstance(
          options?.qualityEvaluator || openAIConfig.qualityEvaluator.id
        )
      } else {
        // Fallback to mixed providers
        this.orchestratorModel = getModelInstance(
          options?.orchestratorModel ||
          getBestModelForTask({ useCase: 'reasoning', prioritizeQuality: true }).id
        )

        this.contentAnalyzer = getModelInstance(
          options?.contentAnalyzer ||
          getBestModelForTask({ useCase: 'analysis', prioritizeSpeed: true }).id
        )

        this.questionGenerator = getModelInstance(
          options?.questionGenerator ||
          getBestModelForTask({ useCase: 'content-generation', prioritizeQuality: true }).id
        )

        this.qualityEvaluator = getModelInstance(
          options?.qualityEvaluator ||
          getBestModelForTask({ useCase: 'analysis', prioritizeQuality: true }).id
        )
      }
    } catch (error) {
      console.error('Error initializing AI models:', error)
      throw new Error('Failed to initialize AI models. Please ensure OpenAI API key is configured.')
    }
  }

  // Main orchestrator method
  async createQuiz(input: {
    content?: string
    files?: Array<{ name: string; content: string; type: string }>
    requirements?: {
      questionCount?: number
      difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
      questionTypes?: string[]
      timeLimit?: number
      targetAudience?: string
      learningObjectives?: string[]
    }
    preferences?: {
      prioritizeQuality?: boolean
      prioritizeSpeed?: boolean
      includeExplanations?: boolean
      includeImages?: boolean
    }
  }) {
    try {
      // Step 1: Analyze content and requirements
      const analysis = await this.analyzeContent(input)
      
      // Step 2: Create quiz structure plan
      const structure = await this.planQuizStructure(analysis, input.requirements)
      
      // Step 3: Generate questions in parallel batches
      const questions = await this.generateQuestions(structure, input.content || '', input.files)
      
      // Step 4: Evaluate and improve quality
      const evaluatedQuestions = await this.evaluateAndImproveQuestions(questions)
      
      // Step 5: Finalize quiz structure
      const finalQuiz = await this.finalizeQuiz(structure, evaluatedQuestions)
      
      return {
        success: true,
        quiz: finalQuiz,
        metadata: {
          analysisUsed: analysis,
          questionsGenerated: evaluatedQuestions.length,
          qualityScore: await this.calculateOverallQuality(evaluatedQuestions),
          processingTime: Date.now()
        }
      }
    } catch (error) {
      console.error('Quiz creation failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        quiz: null
      }
    }
  }

  // Public method for content analysis
  async analyzeContentPublic(input: any) {
    return this.analyzeContent(input)
  }

  private async analyzeContent(input: any) {
    const contentToAnalyze = [
      input.content || '',
      ...(input.files?.map((f: any) => f.content) || [])
    ].join('\n\n')

    const { object: analysis } = await generateObject({
      model: this.contentAnalyzer,
      schema: quizAnalysisSchema,
      system: `You are an expert educational content analyzer. Analyze the provided content and determine the best approach for creating a quiz.`,
      prompt: `Analyze this content for quiz creation:

${contentToAnalyze}

Additional requirements: ${JSON.stringify(input.requirements || {})}

Provide a comprehensive analysis including:
1. Main topic and subtopics
2. Appropriate difficulty level
3. Suggested question count
4. Learning objectives
5. Key topics to cover
6. Target audience
7. Estimated duration
8. Relevant tags
9. Prerequisites needed`
    })

    return analysis
  }

  private async planQuizStructure(analysis: any, requirements?: any) {
    const { text: structurePlan } = await generateText({
      model: this.orchestratorModel,
      system: `You are an expert quiz designer. Create a detailed structure plan for a quiz based on the content analysis and requirements.`,
      prompt: `Based on this analysis: ${JSON.stringify(analysis)}
      
And these requirements: ${JSON.stringify(requirements || {})}

Create a detailed quiz structure plan including:
1. Quiz title and description
2. Question distribution by type and difficulty
3. Learning progression flow
4. Time allocation per section
5. Assessment strategy
6. Instructions for students`
    })

    return structurePlan
  }

  private async generateQuestions(structure: string, content: string, files?: any[]) {
    // Use parallel processing for question generation
    const questionBatches = await this.createQuestionBatches(structure)
    
    const questionPromises = questionBatches.map(async (batch) => {
      return await generateObject({
        model: this.questionGenerator,
        schema: z.object({ questions: z.array(questionSchema) }),
        system: `You are an expert question writer. Create high-quality educational questions that test understanding, not just memorization.`,
        prompt: `Generate ${batch.count} questions of type ${batch.type} with difficulty ${batch.difficulty}.

Content: ${content}
Files: ${JSON.stringify(files?.map(f => ({ name: f.name, type: f.type })) || [])}

Requirements:
- Focus on ${batch.topics.join(', ')}
- Use Bloom's taxonomy level: ${batch.bloomsLevel}
- Include clear explanations
- Ensure questions are unambiguous
- Test practical understanding

Structure: ${structure}`
      })
    })

    const results = await Promise.all(questionPromises)
    return results.flatMap(result => result.object.questions)
  }

  private async createQuestionBatches(_structure: string) {
    // This would be more sophisticated in a real implementation
    return [
      { count: 3, type: 'MCQ', difficulty: 'EASY', topics: ['basics'], bloomsLevel: 'remember' },
      { count: 4, type: 'MCQ', difficulty: 'MEDIUM', topics: ['application'], bloomsLevel: 'apply' },
      { count: 3, type: 'SHORT_ANSWER', difficulty: 'HARD', topics: ['analysis'], bloomsLevel: 'analyze' }
    ]
  }

  private async evaluateAndImproveQuestions(questions: any[]) {
    const evaluationPromises = questions.map(async (question) => {
      const { object: evaluation } = await generateObject({
        model: this.qualityEvaluator,
        schema: z.object({
          qualityScore: z.number().min(0).max(100),
          issues: z.array(z.string()),
          improvements: z.array(z.string()),
          isAcceptable: z.boolean(),
          improvedQuestion: questionSchema.optional()
        }),
        system: `You are an expert in educational assessment quality. Evaluate questions for clarity, accuracy, and educational value.`,
        prompt: `Evaluate this question:
        
${JSON.stringify(question, null, 2)}

Check for:
1. Clarity and unambiguous language
2. Appropriate difficulty level
3. Educational value
4. Correct answer accuracy
5. Quality of distractors (for MCQ)
6. Explanation quality

If the question needs improvement, provide an improved version.`
      })

      return evaluation.isAcceptable
        ? question
        : evaluation.improvedQuestion || question
    })

    return await Promise.all(evaluationPromises)
  }

  private async finalizeQuiz(structure: string, questions: any[]) {
    const { object: finalQuiz } = await generateObject({
      model: this.orchestratorModel,
      schema: quizStructureSchema,
      system: `You are finalizing a quiz. Create the complete quiz structure with metadata.`,
      prompt: `Finalize this quiz:

Structure Plan: ${structure}
Questions: ${JSON.stringify(questions, null, 2)}

Create a complete quiz with:
1. Engaging title and description
2. Clear instructions
3. Properly ordered questions
4. Complete metadata including points, duration, and distributions`
    })

    return finalQuiz
  }

  private async calculateOverallQuality(questions: any[]): Promise<number> {
    // Simple quality calculation - could be more sophisticated
    const scores = await Promise.all(
      questions.map(async (q) => {
        const { object } = await generateObject({
          model: this.qualityEvaluator,
          schema: z.object({ score: z.number().min(0).max(100) }),
          prompt: `Rate this question's quality (0-100): ${JSON.stringify(q)}`
        })
        return object.score
      })
    )
    
    return scores.reduce((sum, score) => sum + score, 0) / scores.length
  }
}
