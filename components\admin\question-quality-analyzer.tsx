"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Lightbulb, 
  TrendingUp,
  Loader2,
  BarChart3,
  Target,
  BookOpen
} from "lucide-react"
import { toast } from "sonner"
import { motion } from "framer-motion"

interface Question {
  id: string
  type: string
  text: string
  options?: string[]
  correctAnswer: string
  explanation?: string
}

interface QualityIssue {
  type: 'clarity' | 'difficulty' | 'options' | 'grammar' | 'bias' | 'accuracy'
  severity: 'low' | 'medium' | 'high'
  description: string
  suggestion: string
}

interface QuestionAnalysis {
  id: string
  qualityScore: number
  issues: QualityIssue[]
  strengths: string[]
  recommendations: string[]
}

interface QualityAnalysis {
  overallScore: number
  overallFeedback: string
  questions: QuestionAnalysis[]
  suggestions: {
    difficultyBalance: string
    questionTypes: string
    contentCoverage: string
    improvements: string[]
  }
}

interface QuestionQualityAnalyzerProps {
  questions: Question[]
  onAnalysisComplete?: (analysis: QualityAnalysis) => void
}

const issueTypeColors = {
  clarity: "bg-blue-100 text-blue-800",
  difficulty: "bg-orange-100 text-orange-800", 
  options: "bg-purple-100 text-purple-800",
  grammar: "bg-red-100 text-red-800",
  bias: "bg-yellow-100 text-yellow-800",
  accuracy: "bg-green-100 text-green-800"
}

const severityColors = {
  low: "border-green-200 bg-green-50",
  medium: "border-yellow-200 bg-yellow-50",
  high: "border-red-200 bg-red-50"
}

const getScoreColor = (score: number) => {
  if (score >= 90) return "text-green-600"
  if (score >= 80) return "text-blue-600"
  if (score >= 70) return "text-yellow-600"
  if (score >= 60) return "text-orange-600"
  return "text-red-600"
}

const getScoreLabel = (score: number) => {
  if (score >= 90) return "Excellent"
  if (score >= 80) return "Good"
  if (score >= 70) return "Fair"
  if (score >= 60) return "Poor"
  return "Needs Work"
}

export function QuestionQualityAnalyzer({ questions, onAnalysisComplete }: QuestionQualityAnalyzerProps) {
  const [analysis, setAnalysis] = useState<QualityAnalysis | null>(null)
  const [loading, setLoading] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  const analyzeQuestions = async () => {
    if (questions.length === 0) {
      toast.error('No questions to analyze')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/quizzes/analyze-quality', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ questions })
      })

      if (response.ok) {
        const data = await response.json()
        setAnalysis(data.data)
        onAnalysisComplete?.(data.data)
        toast.success('Question quality analysis completed')
      } else {
        throw new Error('Failed to analyze questions')
      }
    } catch (error) {
      console.error('Error analyzing questions:', error)
      toast.error('Failed to analyze question quality')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Question Quality Analysis</h3>
          <p className="text-sm text-muted-foreground">
            AI-powered analysis of your quiz questions
          </p>
        </div>
        <Button onClick={analyzeQuestions} disabled={loading || questions.length === 0}>
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <BarChart3 className="h-4 w-4 mr-2" />
          )}
          Analyze Quality
        </Button>
      </div>

      {analysis && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Overall Quality Score
                  </CardTitle>
                  <CardDescription>
                    {analysis.overallFeedback}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className={`text-3xl font-bold ${getScoreColor(analysis.overallScore)}`}>
                    {analysis.overallScore}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {getScoreLabel(analysis.overallScore)}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Progress value={analysis.overallScore} className="h-2" />
            </CardContent>
          </Card>

          {/* Question Breakdown */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Question Analysis ({analysis.questions.length} questions)
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDetails(!showDetails)}
                >
                  {showDetails ? 'Hide Details' : 'Show Details'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {analysis.questions.map((question, index) => (
                  <Card key={question.id} className="relative">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Question {index + 1}</span>
                        <Badge className={getScoreColor(question.qualityScore)}>
                          {question.qualityScore}
                        </Badge>
                      </div>
                      <Progress value={question.qualityScore} className="h-1" />
                    </CardHeader>
                    {showDetails && (
                      <CardContent className="pt-0">
                        {/* Issues */}
                        {question.issues.length > 0 && (
                          <div className="space-y-2 mb-3">
                            <span className="text-xs font-medium text-muted-foreground">Issues:</span>
                            {question.issues.map((issue, issueIndex) => (
                              <Alert key={issueIndex} className={`py-2 ${severityColors[issue.severity]}`}>
                                <AlertTriangle className="h-3 w-3" />
                                <AlertDescription className="text-xs">
                                  <Badge className={`text-xs mr-1 ${issueTypeColors[issue.type]}`}>
                                    {issue.type}
                                  </Badge>
                                  {issue.description}
                                </AlertDescription>
                              </Alert>
                            ))}
                          </div>
                        )}

                        {/* Strengths */}
                        {question.strengths.length > 0 && (
                          <div className="space-y-1 mb-3">
                            <span className="text-xs font-medium text-muted-foreground">Strengths:</span>
                            {question.strengths.map((strength, strengthIndex) => (
                              <div key={strengthIndex} className="flex items-start gap-1">
                                <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                                <span className="text-xs text-green-700">{strength}</span>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Recommendations */}
                        {question.recommendations.length > 0 && (
                          <div className="space-y-1">
                            <span className="text-xs font-medium text-muted-foreground">Recommendations:</span>
                            {question.recommendations.map((rec, recIndex) => (
                              <div key={recIndex} className="flex items-start gap-1">
                                <Lightbulb className="h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0" />
                                <span className="text-xs text-blue-700">{rec}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Suggestions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Improvement Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Difficulty Balance</h4>
                <p className="text-sm text-muted-foreground">{analysis.suggestions.difficultyBalance}</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Question Types</h4>
                <p className="text-sm text-muted-foreground">{analysis.suggestions.questionTypes}</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Content Coverage</h4>
                <p className="text-sm text-muted-foreground">{analysis.suggestions.contentCoverage}</p>
              </div>
              {analysis.suggestions.improvements.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">General Improvements</h4>
                  <ul className="space-y-1">
                    {analysis.suggestions.improvements.map((improvement, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
                        <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
