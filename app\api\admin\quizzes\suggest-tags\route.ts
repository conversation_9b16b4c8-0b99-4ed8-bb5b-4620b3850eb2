import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { createOpenAI } from '@ai-sdk/openai'
import { generateObject } from 'ai'
import { z } from 'zod'

const tagSuggestionSchema = z.object({
  content: z.string().min(1, "Content is required for tag suggestions"),
  existingTags: z.array(z.string()).optional()
})

const suggestedTagsSchema = z.object({
  tags: z.array(z.object({
    name: z.string(),
    category: z.enum(['subject', 'topic', 'difficulty', 'skill', 'format']),
    relevance: z.number().min(0).max(1),
    description: z.string()
  }))
})

// POST /api/admin/quizzes/suggest-tags - Get AI-powered tag suggestions
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: tagSuggestionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { content, existingTags = [] } = validatedBody

      // Get popular tags from existing quizzes
      const popularTags = await prisma.quiz.findMany({
        select: { tags: true },
        where: { isPublished: true },
        take: 100
      })

      const tagFrequency = new Map<string, number>()
      popularTags.forEach(quiz => {
        quiz.tags.forEach(tag => {
          tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1)
        })
      })

      const mostPopularTags = Array.from(tagFrequency.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([tag]) => tag)

      // Create OpenAI client
      const openai = createOpenAI({
        apiKey: process.env.OPENAI_API_KEY
      })

      // Generate AI-powered tag suggestions
      const result = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: suggestedTagsSchema,
        prompt: `
          Analyze the following quiz content and suggest relevant tags:
          
          Content: "${content}"
          
          Existing tags: ${existingTags.join(', ')}
          Popular tags in system: ${mostPopularTags.join(', ')}
          
          Suggest 8-12 relevant tags categorized as:
          - subject: Academic subjects (e.g., "mathematics", "biology", "history")
          - topic: Specific topics (e.g., "algebra", "photosynthesis", "world-war-2")
          - difficulty: Difficulty indicators (e.g., "beginner", "intermediate", "advanced")
          - skill: Skills being tested (e.g., "problem-solving", "critical-thinking", "memorization")
          - format: Quiz format (e.g., "multiple-choice", "true-false", "short-answer")
          
          For each tag:
          - Provide a relevance score (0-1) based on how well it matches the content
          - Include a brief description explaining why this tag is relevant
          - Avoid duplicating existing tags
          - Prefer tags that are already popular in the system when appropriate
          
          Focus on tags that would help students find this quiz and help administrators organize content.
        `
      })

      // Filter out existing tags and sort by relevance
      const filteredTags = result.object.tags
        .filter(tag => !existingTags.includes(tag.name))
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, 10)

      return APIResponse.success({
        suggestions: filteredTags,
        popularTags: mostPopularTags.slice(0, 10),
        totalExistingTags: tagFrequency.size
      }, 'Tag suggestions generated successfully')

    } catch (error: any) {
      console.error('Error generating tag suggestions:', error)
      return APIResponse.error(
        error.message || 'Failed to generate tag suggestions',
        500
      )
    }
  }
)

// GET /api/admin/quizzes/suggest-tags - Get popular tags and categories
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      // Get all tags from published quizzes
      const quizzes = await prisma.quiz.findMany({
        select: { tags: true },
        where: { isPublished: true }
      })

      const tagFrequency = new Map<string, number>()
      quizzes.forEach(quiz => {
        quiz.tags.forEach(tag => {
          tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1)
        })
      })

      // Sort tags by frequency
      const popularTags = Array.from(tagFrequency.entries())
        .sort(([,a], [,b]) => b - a)
        .map(([tag, count]) => ({ name: tag, count }))

      // Categorize popular tags (simple heuristic-based categorization)
      const categories = {
        subjects: popularTags.filter(({ name }) => 
          ['math', 'science', 'history', 'english', 'physics', 'chemistry', 'biology', 'geography'].some(subject => 
            name.toLowerCase().includes(subject)
          )
        ).slice(0, 10),
        
        difficulties: popularTags.filter(({ name }) => 
          ['easy', 'medium', 'hard', 'beginner', 'intermediate', 'advanced', 'basic'].some(diff => 
            name.toLowerCase().includes(diff)
          )
        ).slice(0, 5),
        
        topics: popularTags.filter(({ name }) => 
          !['easy', 'medium', 'hard', 'beginner', 'intermediate', 'advanced', 'basic'].some(diff => 
            name.toLowerCase().includes(diff)
          ) && !['math', 'science', 'history', 'english', 'physics', 'chemistry', 'biology', 'geography'].some(subject => 
            name.toLowerCase().includes(subject)
          )
        ).slice(0, 15)
      }

      return APIResponse.success({
        popularTags: popularTags.slice(0, 20),
        categories,
        totalTags: tagFrequency.size
      }, 'Popular tags retrieved successfully')

    } catch (error: any) {
      console.error('Error fetching popular tags:', error)
      return APIResponse.error(
        error.message || 'Failed to fetch popular tags',
        500
      )
    }
  }
)
