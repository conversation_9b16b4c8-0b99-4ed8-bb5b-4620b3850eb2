# AI Quiz Creator Setup Guide

## Quick Start

To use the AI-powered quiz creation system, you need to configure at least one AI provider API key.

### Step 1: Get API Keys

You need at least **one** of these API keys (OpenAI is recommended for the best experience):

#### OpenAI (Recommended)
1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create an account or sign in
3. Generate a new API key
4. Copy the key (starts with `sk-`)

#### Anthropic (Recommended for Quality)
1. Go to [Anthropic Console](https://console.anthropic.com/)
2. Create an account or sign in
3. Generate a new API key
4. Copy the key

#### Google AI (Optional)
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create or select a project
3. Generate an API key
4. Copy the key

#### Other Providers (Optional)
- **Groq**: [Groq Console](https://console.groq.com/keys) - For ultra-fast inference
- **xAI**: [xAI Console](https://console.x.ai/) - For Grok models
- **DeepSeek**: [DeepSeek Platform](https://platform.deepseek.com/) - For reasoning tasks

### Step 2: Configure Environment Variables

1. Create a `.env.local` file in your project root
2. Add your API keys:

```env
# Required: At least one of these
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Additional providers
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
XAI_API_KEY=your_xai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
```

### Step 3: Restart Your Development Server

After adding the API keys, restart your development server:

```bash
npm run dev
```

## Usage

### Creating a Quiz with AI

1. Go to **Admin Panel** → **Quiz Management** → **Create Quiz**
2. Click the **"AI Quiz Creator"** button (purple gradient button)
3. Follow the 3-step process:
   - **Step 1**: Upload content or paste text
   - **Step 2**: Configure settings and AI models
   - **Step 3**: Generate your quiz

### AI Settings Management

Visit **Admin Panel** → **AI Management** to:
- Configure default AI models for different tasks
- Monitor AI agent performance
- Set cost limits and preferences
- View real-time AI usage statistics

## Recommended Configurations

### For Best Quality
```
Orchestrator: GPT-4o or Claude 3.5 Sonnet
Content Analyzer: Claude 3.5 Sonnet
Question Generator: GPT-4o
Quality Evaluator: Claude 3.5 Sonnet
```

### For Speed
```
Orchestrator: GPT-4o Mini
Content Analyzer: Claude 3.5 Haiku
Question Generator: GPT-4o Mini
Quality Evaluator: Claude 3.5 Haiku
```

### For Cost Efficiency
```
Orchestrator: Gemini 2.0 Flash
Content Analyzer: Gemini 2.0 Flash
Question Generator: GPT-4o Mini
Quality Evaluator: Claude 3.5 Haiku
```

## Troubleshooting

### "Analysis failed" Error
- **Cause**: Missing or invalid API keys
- **Solution**: Check your `.env.local` file and ensure API keys are correct
- **Check**: Restart your development server after adding keys

### "No AI models available" Error
- **Cause**: No valid API keys configured
- **Solution**: Add at least one API key to your `.env.local` file

### API Key Not Working
- **Check**: API key format (OpenAI keys start with `sk-`)
- **Check**: API key has sufficient credits/quota
- **Check**: No extra spaces or quotes around the key in `.env.local`

### Slow Generation
- **Solution**: Use faster models like Groq or Claude Haiku
- **Solution**: Enable "Prioritize Speed" in preferences

## Cost Management

### Estimated Costs (per quiz with 10 questions)
- **GPT-4o**: ~$0.10-0.20
- **GPT-4o Mini**: ~$0.02-0.05
- **Claude 3.5 Sonnet**: ~$0.15-0.25
- **Claude 3.5 Haiku**: ~$0.03-0.06
- **Gemini 2.0 Flash**: ~$0.01-0.03

### Cost Optimization Tips
1. Use cheaper models for content analysis
2. Use premium models only for question generation
3. Set daily spending limits in AI Settings
4. Monitor usage in the AI Dashboard

## Support

If you encounter issues:
1. Check this setup guide
2. Verify your API keys are correct
3. Check the browser console for detailed error messages
4. Ensure your development server is restarted after adding keys

## Security Notes

- Never commit `.env.local` to version control
- Keep your API keys secure and private
- Regularly rotate your API keys
- Monitor your API usage and costs
