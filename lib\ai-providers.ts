import { openai } from '@ai-sdk/openai'
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'
import { xai } from '@ai-sdk/xai'
import { mistral } from '@ai-sdk/mistral'
import { groq } from '@ai-sdk/groq'
import { deepseek } from '@ai-sdk/deepseek'

export interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  capabilities: {
    textGeneration: boolean
    imageInput: boolean
    toolCalling: boolean
    streaming: boolean
    objectGeneration: boolean
    reasoning: boolean
  }
  pricing: {
    input: number // per 1M tokens
    output: number // per 1M tokens
  }
  contextWindow: number
  maxOutput: number
  speed: 'fast' | 'medium' | 'slow'
  quality: 'high' | 'medium' | 'low'
  useCase: string[]
}

export const AI_MODELS: AIModel[] = [
  // OpenAI Models
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    description: 'Most capable multimodal model, great for complex reasoning',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 2.5, output: 10 },
    contextWindow: 128000,
    maxOutput: 16384,
    speed: 'medium',
    quality: 'high',
    useCase: ['complex-analysis', 'content-generation', 'reasoning', 'multimodal']
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    description: 'Fast and cost-effective, great for simple tasks',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.15, output: 0.6 },
    contextWindow: 128000,
    maxOutput: 16384,
    speed: 'fast',
    quality: 'medium',
    useCase: ['simple-tasks', 'classification', 'basic-generation']
  },
  {
    id: 'o3-mini',
    name: 'o3 Mini',
    provider: 'OpenAI',
    description: 'Advanced reasoning model for complex problem solving',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 3.0, output: 12 },
    contextWindow: 200000,
    maxOutput: 65536,
    speed: 'slow',
    quality: 'high',
    useCase: ['reasoning', 'problem-solving', 'analysis', 'complex-tasks']
  },

  // Anthropic Models
  {
    id: 'claude-3-5-sonnet-20241022',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for analysis, writing, and complex reasoning',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 3.0, output: 15 },
    contextWindow: 200000,
    maxOutput: 8192,
    speed: 'medium',
    quality: 'high',
    useCase: ['analysis', 'writing', 'reasoning', 'content-creation']
  },
  {
    id: 'claude-3-5-haiku-20241022',
    name: 'Claude 3.5 Haiku',
    provider: 'Anthropic',
    description: 'Fast and efficient for quick tasks',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.8, output: 4.0 },
    contextWindow: 200000,
    maxOutput: 8192,
    speed: 'fast',
    quality: 'medium',
    useCase: ['quick-tasks', 'classification', 'simple-analysis']
  },

  // Google Models
  {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash',
    provider: 'Google',
    description: 'Latest multimodal model with excellent performance',
    capabilities: {
      textGeneration: true,
      imageInput: true,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 0.075, output: 0.3 },
    contextWindow: 1000000,
    maxOutput: 8192,
    speed: 'fast',
    quality: 'high',
    useCase: ['multimodal', 'long-context', 'analysis', 'generation']
  },

  // xAI Models
  {
    id: 'grok-3',
    name: 'Grok 3',
    provider: 'xAI',
    description: 'Advanced reasoning with real-time information',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 2.0, output: 8.0 },
    contextWindow: 131072,
    maxOutput: 4096,
    speed: 'medium',
    quality: 'high',
    useCase: ['reasoning', 'real-time', 'analysis', 'problem-solving']
  },

  // DeepSeek Models
  {
    id: 'deepseek-reasoner',
    name: 'DeepSeek Reasoner',
    provider: 'DeepSeek',
    description: 'Specialized reasoning model for complex problems',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: true
    },
    pricing: { input: 0.55, output: 2.19 },
    contextWindow: 64000,
    maxOutput: 8192,
    speed: 'slow',
    quality: 'high',
    useCase: ['reasoning', 'problem-solving', 'analysis', 'mathematics']
  },

  // Groq Models (Fast inference)
  {
    id: 'llama-3.3-70b-versatile',
    name: 'Llama 3.3 70B',
    provider: 'Groq',
    description: 'Ultra-fast inference for real-time applications',
    capabilities: {
      textGeneration: true,
      imageInput: false,
      toolCalling: true,
      streaming: true,
      objectGeneration: true,
      reasoning: false
    },
    pricing: { input: 0.59, output: 0.79 },
    contextWindow: 131072,
    maxOutput: 32768,
    speed: 'fast',
    quality: 'medium',
    useCase: ['real-time', 'fast-generation', 'chat', 'simple-tasks']
  }
]

export function getModelInstance(modelId: string) {
  const model = AI_MODELS.find(m => m.id === modelId)
  if (!model) {
    throw new Error(`Model ${modelId} not found`)
  }

  switch (model.provider) {
    case 'OpenAI':
      return openai(modelId)
    case 'Anthropic':
      return anthropic(modelId)
    case 'Google':
      return google(modelId)
    case 'xAI':
      return xai(modelId)
    case 'Mistral':
      return mistral(modelId)
    case 'Groq':
      return groq(modelId)
    case 'DeepSeek':
      return deepseek(modelId)
    default:
      throw new Error(`Provider ${model.provider} not supported`)
  }
}

export function getModelsByUseCase(useCase: string): AIModel[] {
  return AI_MODELS.filter(model => model.useCase.includes(useCase))
}

export function getModelsBySpeed(speed: 'fast' | 'medium' | 'slow'): AIModel[] {
  return AI_MODELS.filter(model => model.speed === speed)
}

export function getModelsByQuality(quality: 'high' | 'medium' | 'low'): AIModel[] {
  return AI_MODELS.filter(model => model.quality === quality)
}

export function getBestModelForTask(task: {
  useCase: string
  prioritizeSpeed?: boolean
  prioritizeQuality?: boolean
  maxCost?: number
}): AIModel {
  let candidates = getModelsByUseCase(task.useCase)
  
  if (task.maxCost) {
    candidates = candidates.filter(model => model.pricing.input <= task.maxCost)
  }
  
  if (task.prioritizeSpeed) {
    candidates.sort((a, b) => {
      const speedOrder = { fast: 3, medium: 2, slow: 1 }
      return speedOrder[b.speed] - speedOrder[a.speed]
    })
  } else if (task.prioritizeQuality) {
    candidates.sort((a, b) => {
      const qualityOrder = { high: 3, medium: 2, low: 1 }
      return qualityOrder[b.quality] - qualityOrder[a.quality]
    })
  }
  
  return candidates[0] || AI_MODELS[0] // fallback to first model
}
