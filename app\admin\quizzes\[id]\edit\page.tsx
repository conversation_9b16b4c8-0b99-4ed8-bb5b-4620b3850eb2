"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft, Save, Plus, Trash2, Edit } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import { QuestionEditor } from "@/components/admin/question-editor"
import { EnhancedTagInput } from "@/components/admin/enhanced-tag-input"

interface QuizData {
  id: string
  title: string
  description: string
  type: string
  difficulty: string
  tags: string[]
  timeLimit: number
  maxAttempts: number
  passingScore: number
  instructions: string
  isPublished: boolean
  startTime: string | null
  endTime: string | null
}

interface Question {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'MATCHING'
  text: string
  options: string[]
  correctAnswer: string
  explanation: string
  points: number
  order: number
  image?: string
}

export default function EditQuizPage() {
  const params = useParams()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [quiz, setQuiz] = useState<QuizData | null>(null)
  const [questions, setQuestions] = useState<Question[]>([])
  const [newTag, setNewTag] = useState("")
  const [showQuestionEditor, setShowQuestionEditor] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<Question | undefined>()

  useEffect(() => {
    fetchQuizData()
  }, [params.id])

  const fetchQuizData = async () => {
    try {
      const response = await fetch(`/api/admin/quizzes/${params.id}`)
      if (!response.ok) throw new Error('Failed to fetch quiz')

      const data = await response.json()
      setQuiz({
        id: data.id,
        title: data.title,
        description: data.description || '',
        type: data.type,
        difficulty: data.difficulty,
        tags: data.tags || [],
        timeLimit: data.timeLimit || 60,
        maxAttempts: data.maxAttempts || 1,
        passingScore: data.passingScore || 70,
        instructions: data.instructions || '',
        isPublished: data.isPublished,
        startTime: data.startTime,
        endTime: data.endTime
      })

      // Set questions if they exist
      if (data.questions) {
        setQuestions(data.questions.map((q: any) => ({
          id: q.id,
          type: q.type,
          text: q.text,
          options: q.options || [],
          correctAnswer: q.correctAnswer || '',
          explanation: q.explanation || '',
          points: q.points || 1,
          order: q.order || 0,
          image: q.image
        })))
      }
    } catch (error) {
      console.error('Error fetching quiz:', error)
      toast.error('Failed to load quiz data')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!quiz) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/quizzes/${params.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...quiz,
          questions: questions
        })
      })

      if (!response.ok) throw new Error('Failed to update quiz')

      toast.success('Quiz updated successfully!')
      router.push(`/admin/quizzes/${params.id}`)
    } catch (error) {
      console.error('Error updating quiz:', error)
      toast.error('Failed to update quiz')
    } finally {
      setSaving(false)
    }
  }

  const addTag = () => {
    if (newTag.trim() && quiz && !quiz.tags.includes(newTag.trim())) {
      setQuiz({ ...quiz, tags: [...quiz.tags, newTag.trim()] })
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    if (quiz) {
      setQuiz({ ...quiz, tags: quiz.tags.filter(tag => tag !== tagToRemove) })
    }
  }

  const handleQuestionSave = (question: Question) => {
    if (editingQuestion) {
      // Update existing question
      setQuestions(prev => prev.map(q => q.id === question.id ? question : q))
    } else {
      // Add new question
      setQuestions(prev => [...prev, { ...question, order: prev.length }])
    }
    setShowQuestionEditor(false)
    setEditingQuestion(undefined)
  }

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question)
    setShowQuestionEditor(true)
  }

  const handleDeleteQuestion = (questionId: string) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId))
  }

  if (loading) {
    return <div className="p-6">Loading...</div>
  }

  if (!quiz) {
    return <div className="p-6">Quiz not found</div>
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/quizzes/${params.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quiz
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Edit Quiz</h1>
            <p className="text-muted-foreground mt-1">
              Modify quiz settings and content
            </p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update the basic details of your quiz
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={quiz.title}
                onChange={(e) => setQuiz({ ...quiz, title: e.target.value })}
                placeholder="Enter quiz title"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={quiz.description}
                onChange={(e) => setQuiz({ ...quiz, description: e.target.value })}
                placeholder="Enter quiz description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Type</Label>
                <Select value={quiz.type} onValueChange={(value) => setQuiz({ ...quiz, type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="QUIZ">Quiz</SelectItem>
                    <SelectItem value="TEST_SERIES">Test Series</SelectItem>
                    <SelectItem value="DAILY_PRACTICE">Daily Practice</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="difficulty">Difficulty</Label>
                <Select value={quiz.difficulty} onValueChange={(value) => setQuiz({ ...quiz, difficulty: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="EASY">Easy</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HARD">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <EnhancedTagInput
              tags={quiz.tags}
              onTagsChange={(tags) => setQuiz({ ...quiz, tags })}
              quizContent={`${quiz.title} ${quiz.description} ${quiz.instructions}`.trim()}
              placeholder="Add a tag"
              maxTags={10}
            />
          </CardContent>
        </Card>

        {/* Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Quiz Settings</CardTitle>
            <CardDescription>
              Configure quiz behavior and constraints
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                <Input
                  id="timeLimit"
                  type="number"
                  value={quiz.timeLimit}
                  onChange={(e) => setQuiz({ ...quiz, timeLimit: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div>
                <Label htmlFor="maxAttempts">Max Attempts</Label>
                <Input
                  id="maxAttempts"
                  type="number"
                  value={quiz.maxAttempts}
                  onChange={(e) => setQuiz({ ...quiz, maxAttempts: parseInt(e.target.value) || 1 })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="passingScore">Passing Score (%)</Label>
              <Input
                id="passingScore"
                type="number"
                value={quiz.passingScore}
                onChange={(e) => setQuiz({ ...quiz, passingScore: parseInt(e.target.value) || 0 })}
                min="0"
                max="100"
              />
            </div>

            <div>
              <Label htmlFor="instructions">Instructions</Label>
              <Textarea
                id="instructions"
                value={quiz.instructions}
                onChange={(e) => setQuiz({ ...quiz, instructions: e.target.value })}
                placeholder="Enter quiz instructions"
                rows={4}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="published"
                checked={quiz.isPublished}
                onCheckedChange={(checked) => setQuiz({ ...quiz, isPublished: checked })}
              />
              <Label htmlFor="published">Published</Label>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Questions Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Quiz Questions</CardTitle>
              <CardDescription>
                Manage the questions for this quiz
              </CardDescription>
            </div>
            <Button onClick={() => setShowQuestionEditor(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Question
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {questions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No questions added yet. Click "Add Question" to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {questions.map((question, index) => (
                <Card key={question.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <CardTitle className="text-base">Question {index + 1}</CardTitle>
                        <Badge variant="outline">{question.type}</Badge>
                        <Badge variant="secondary">{question.points} pt{question.points !== 1 ? 's' : ''}</Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleEditQuestion(question)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDeleteQuestion(question.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-2">{question.text}</p>
                    {question.options.length > 0 && (
                      <div className="space-y-1">
                        {question.options.map((option, optIndex) => (
                          <div key={optIndex} className="flex items-center gap-2 text-sm">
                            <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                              option === question.correctAnswer
                                ? 'bg-green-100 text-green-800 border-2 border-green-300'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {String.fromCharCode(65 + optIndex)}
                            </span>
                            <span className={option === question.correctAnswer ? 'font-medium text-green-800' : ''}>
                              {option}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Question Editor Modal */}
      {showQuestionEditor && (
        <QuestionEditor
          question={editingQuestion}
          onSave={handleQuestionSave}
          onCancel={() => {
            setShowQuestionEditor(false)
            setEditingQuestion(undefined)
          }}
        />
      )}
    </div>
  )
}
