import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { createOpenAI } from '@ai-sdk/openai'
import { generateObject } from 'ai'
import { z } from 'zod'

const difficultyAssessmentSchema = z.object({
  content: z.string().min(1, "Content is required for difficulty assessment"),
  questions: z.array(z.object({
    id: z.string(),
    type: z.string(),
    text: z.string(),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string()
  })).optional()
})

const assessmentResultSchema = z.object({
  overallDifficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  factors: z.object({
    conceptComplexity: z.enum(['low', 'medium', 'high']),
    vocabularyLevel: z.enum(['basic', 'intermediate', 'advanced']),
    cognitiveLoad: z.enum(['low', 'medium', 'high']),
    prerequisiteKnowledge: z.enum(['minimal', 'moderate', 'extensive'])
  }),
  questions: z.array(z.object({
    id: z.string(),
    suggestedDifficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
    confidence: z.number().min(0).max(1),
    reasoning: z.string(),
    adjustmentSuggestions: z.array(z.string())
  })).optional(),
  recommendations: z.array(z.string())
})

// POST /api/admin/quizzes/assess-difficulty - Assess quiz difficulty using AI
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: difficultyAssessmentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { content, questions = [] } = validatedBody

      // Create OpenAI client
      if (!process.env.OPENAI_API_KEY) {
        return APIResponse.error('OpenAI API key not configured', 503)
      }

      const openai = createOpenAI({
        apiKey: "********************************************************************************************************************************************"
      })

      // Prepare content for analysis
      let analysisContent = `Quiz Content: ${content}`
      
      if (questions.length > 0) {
        const questionsText = questions.map((q, index) => `
          Question ${index + 1} (${q.type}):
          ${q.text}
          ${q.options ? `Options: ${q.options.join(', ')}` : ''}
          Correct Answer: ${q.correctAnswer}
        `).join('\n')
        
        analysisContent += `\n\nQuestions:\n${questionsText}`
      }

      // Generate difficulty assessment
      const result = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: assessmentResultSchema,
        prompt: `
          As an educational assessment expert, analyze the difficulty level of this quiz content:

          ${analysisContent}

          Provide a comprehensive difficulty assessment including:

          1. Overall Difficulty Classification:
             - Classify as EASY, MEDIUM, or HARD
             - Provide confidence level (0-1)
             - Explain your reasoning

          2. Difficulty Factors Analysis:
             - Concept Complexity: How complex are the underlying concepts?
             - Vocabulary Level: What reading/vocabulary level is required?
             - Cognitive Load: How much mental processing is required?
             - Prerequisite Knowledge: How much prior knowledge is needed?

          3. Individual Question Assessment (if questions provided):
             - Suggest difficulty level for each question
             - Provide confidence and reasoning
             - Suggest adjustments to modify difficulty

          4. Recommendations:
             - How to adjust difficulty if needed
             - Suggestions for improvement
             - Target audience considerations

          Consider these factors:
          - Bloom's Taxonomy levels (remember, understand, apply, analyze, evaluate, create)
          - Cognitive complexity of the subject matter
          - Language complexity and vocabulary requirements
          - Amount of prerequisite knowledge needed
          - Time pressure and question format
          - Target audience (grade level, expertise level)

          Base your assessment on educational research and best practices.
        `
      })

      return APIResponse.success(
        result.object,
        'Difficulty assessment completed successfully'
      )

    } catch (error: any) {
      console.error('Error assessing difficulty:', error)
      return APIResponse.error(
        error.message || 'Failed to assess difficulty',
        500
      )
    }
  }
)

// GET /api/admin/quizzes/assess-difficulty/guidelines - Get difficulty guidelines
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const guidelines = {
        difficultyLevels: {
          EASY: {
            description: "Basic recall and understanding",
            characteristics: [
              "Simple vocabulary and concepts",
              "Direct recall of facts",
              "Basic comprehension questions",
              "Minimal prerequisite knowledge",
              "Clear, straightforward language"
            ],
            bloomsLevels: ["Remember", "Understand"],
            targetAudience: "Beginners, introductory level"
          },
          MEDIUM: {
            description: "Application and analysis",
            characteristics: [
              "Moderate vocabulary complexity",
              "Application of concepts",
              "Some analysis required",
              "Moderate prerequisite knowledge",
              "Multi-step problem solving"
            ],
            bloomsLevels: ["Apply", "Analyze"],
            targetAudience: "Intermediate level, some experience"
          },
          HARD: {
            description: "Synthesis and evaluation",
            characteristics: [
              "Advanced vocabulary and concepts",
              "Complex problem solving",
              "Evaluation and synthesis",
              "Extensive prerequisite knowledge",
              "Abstract thinking required"
            ],
            bloomsLevels: ["Evaluate", "Create"],
            targetAudience: "Advanced level, experts"
          }
        },
        assessmentFactors: {
          conceptComplexity: {
            low: "Basic, concrete concepts",
            medium: "Moderate abstraction, some complexity",
            high: "Highly abstract, complex relationships"
          },
          vocabularyLevel: {
            basic: "Common, everyday language",
            intermediate: "Some technical terms, moderate complexity",
            advanced: "Specialized terminology, complex language"
          },
          cognitiveLoad: {
            low: "Simple recall, minimal processing",
            medium: "Some analysis, moderate processing",
            high: "Complex analysis, high mental effort"
          },
          prerequisiteKnowledge: {
            minimal: "Little to no prior knowledge needed",
            moderate: "Some background knowledge helpful",
            extensive: "Significant prior knowledge required"
          }
        },
        adjustmentStrategies: {
          makeEasier: [
            "Simplify vocabulary and language",
            "Break complex questions into smaller parts",
            "Provide more context and background",
            "Use more direct, concrete examples",
            "Reduce the number of steps required"
          ],
          makeHarder: [
            "Use more advanced vocabulary",
            "Require deeper analysis and synthesis",
            "Remove some context clues",
            "Add multi-step problem solving",
            "Require application to new situations"
          ]
        }
      }

      return APIResponse.success(guidelines, 'Difficulty guidelines retrieved successfully')

    } catch (error: any) {
      console.error('Error fetching difficulty guidelines:', error)
      return APIResponse.error(
        error.message || 'Failed to fetch difficulty guidelines',
        500
      )
    }
  }
)
