import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { QuizCreationAgent } from '@/lib/ai-agents/quiz-creation-agent'
import { z } from 'zod'

const quizCreationRequestSchema = z.object({
  content: z.string().optional(),
  files: z.array(z.object({
    name: z.string(),
    content: z.string(),
    type: z.string()
  })).optional(),
  requirements: z.object({
    questionCount: z.number().min(5).max(50).optional(),
    difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']).optional(),
    questionTypes: z.array(z.string()).optional(),
    timeLimit: z.number().optional(),
    targetAudience: z.string().optional(),
    learningObjectives: z.array(z.string()).optional()
  }).optional(),
  preferences: z.object({
    prioritizeQuality: z.boolean().optional(),
    prioritizeSpeed: z.boolean().optional(),
    includeExplanations: z.boolean().optional(),
    includeImages: z.boolean().optional()
  }).optional(),
  modelConfig: z.object({
    orchestratorModel: z.string().optional(),
    contentAnalyzer: z.string().optional(),
    questionGenerator: z.string().optional(),
    qualityEvaluator: z.string().optional()
  }).optional()
})

// POST /api/ai/quiz-creation - Create quiz using AI agents
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: quizCreationRequestSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const {
        content,
        files,
        requirements,
        preferences,
        modelConfig
      } = validatedBody

      // Validate input
      if (!content && (!files || files.length === 0)) {
        return APIResponse.error('Either content or files must be provided', 400)
      }

      // Create AI agent with custom model configuration
      const agent = new QuizCreationAgent(modelConfig)

      // Generate quiz using AI agent
      const result = await agent.createQuiz({
        content,
        files,
        requirements,
        preferences
      })

      if (!result.success) {
        return APIResponse.error(result.error || 'Failed to create quiz', 500)
      }

      return APIResponse.success({
        quiz: result.quiz,
        metadata: result.metadata,
        generatedAt: new Date().toISOString(),
        userId: user.id
      }, 'Quiz created successfully using AI agents')

    } catch (error: any) {
      console.error('AI Quiz Creation Error:', error)
      return APIResponse.error(
        error.message || 'Failed to create quiz with AI',
        500
      )
    }
  }
)

// GET /api/ai/quiz-creation/models - Get available AI models
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const { AI_MODELS, getModelsByUseCase, getBestModelForTask } = await import('@/lib/ai-providers')
      
      const modelsByUseCase = {
        reasoning: getModelsByUseCase('reasoning'),
        analysis: getModelsByUseCase('analysis'),
        contentGeneration: getModelsByUseCase('content-generation'),
        complexAnalysis: getModelsByUseCase('complex-analysis')
      }

      const recommendations = {
        orchestrator: getBestModelForTask({ useCase: 'reasoning', prioritizeQuality: true }),
        contentAnalyzer: getBestModelForTask({ useCase: 'analysis', prioritizeSpeed: true }),
        questionGenerator: getBestModelForTask({ useCase: 'content-generation', prioritizeQuality: true }),
        qualityEvaluator: getBestModelForTask({ useCase: 'analysis', prioritizeQuality: true })
      }

      return APIResponse.success({
        allModels: AI_MODELS,
        modelsByUseCase,
        recommendations,
        defaultConfig: {
          orchestratorModel: recommendations.orchestrator.id,
          contentAnalyzer: recommendations.contentAnalyzer.id,
          questionGenerator: recommendations.questionGenerator.id,
          qualityEvaluator: recommendations.qualityEvaluator.id
        }
      }, 'AI models retrieved successfully')

    } catch (error: any) {
      console.error('Error fetching AI models:', error)
      return APIResponse.error(
        error.message || 'Failed to fetch AI models',
        500
      )
    }
  }
)


