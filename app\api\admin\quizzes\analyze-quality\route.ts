import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { createOpenAI } from '@ai-sdk/openai'
import { generateObject } from 'ai'
import { z } from 'zod'

const qualityAnalysisSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    type: z.string(),
    text: z.string(),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string(),
    explanation: z.string().optional()
  }))
})

const analysisResultSchema = z.object({
  overallScore: z.number().min(0).max(100),
  overallFeedback: z.string(),
  questions: z.array(z.object({
    id: z.string(),
    qualityScore: z.number().min(0).max(100),
    issues: z.array(z.object({
      type: z.enum(['clarity', 'difficulty', 'options', 'grammar', 'bias', 'accuracy']),
      severity: z.enum(['low', 'medium', 'high']),
      description: z.string(),
      suggestion: z.string()
    })),
    strengths: z.array(z.string()),
    recommendations: z.array(z.string())
  })),
  suggestions: z.object({
    difficultyBalance: z.string(),
    questionTypes: z.string(),
    contentCoverage: z.string(),
    improvements: z.array(z.string())
  })
})

// POST /api/admin/quizzes/analyze-quality - Analyze quiz question quality
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: qualityAnalysisSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { questions } = validatedBody

      if (questions.length === 0) {
        return APIResponse.error('No questions provided for analysis', 400)
      }

      // Create OpenAI client
      if (!process.env.OPENAI_API_KEY) {
        return APIResponse.error('OpenAI API key not configured', 503)
      }

      const openai = createOpenAI({
        apiKey: process.env.OPENAI_API_KEY
      })

      // Prepare questions for analysis
      const questionsText = questions.map((q, index) => `
        Question ${index + 1} (${q.type}):
        ${q.text}
        ${q.options ? `Options: ${q.options.join(', ')}` : ''}
        Correct Answer: ${q.correctAnswer}
        ${q.explanation ? `Explanation: ${q.explanation}` : ''}
      `).join('\n\n')

      // Generate quality analysis
      const result = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: analysisResultSchema,
        prompt: `
          As an educational assessment expert, analyze the quality of these quiz questions:

          ${questionsText}

          Provide a comprehensive quality analysis including:

          1. Overall Assessment:
             - Overall quality score (0-100)
             - General feedback on the quiz quality

          2. Individual Question Analysis:
             For each question, evaluate:
             - Quality score (0-100)
             - Identify issues in these categories:
               * clarity: Is the question clear and unambiguous?
               * difficulty: Is the difficulty appropriate and consistent?
               * options: Are the answer choices well-designed (for MCQ)?
               * grammar: Are there grammatical or spelling errors?
               * bias: Is the question free from cultural or demographic bias?
               * accuracy: Is the content factually correct?
             - List strengths of the question
             - Provide specific recommendations for improvement

          3. Quiz-Level Suggestions:
             - Comment on difficulty balance across questions
             - Assess variety of question types
             - Evaluate content coverage
             - Suggest overall improvements

          Focus on:
          - Educational best practices
          - Accessibility and inclusivity
          - Clear, unambiguous language
          - Appropriate difficulty progression
          - Engaging and relevant content
          - Avoiding common question-writing pitfalls

          Be constructive and specific in your feedback.
        `
      })

      return APIResponse.success(
        result.object,
        'Question quality analysis completed successfully'
      )

    } catch (error: any) {
      console.error('Error analyzing question quality:', error)
      return APIResponse.error(
        error.message || 'Failed to analyze question quality',
        500
      )
    }
  }
)

// GET /api/admin/quizzes/analyze-quality/guidelines - Get quality guidelines
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const guidelines = {
        questionWriting: {
          clarity: [
            "Use clear, concise language",
            "Avoid double negatives",
            "Be specific and unambiguous",
            "Use familiar vocabulary for the target audience"
          ],
          structure: [
            "Start with the main idea",
            "Keep questions focused on one concept",
            "Use parallel structure in answer choices",
            "Avoid 'all of the above' or 'none of the above'"
          ],
          difficulty: [
            "Match difficulty to learning objectives",
            "Progress from easier to harder questions",
            "Avoid trick questions",
            "Test understanding, not memorization when possible"
          ]
        },
        answerChoices: {
          mcq: [
            "Make all options plausible",
            "Keep options similar in length",
            "Avoid overlapping or contradictory options",
            "Use 3-4 options for most questions"
          ],
          trueFalse: [
            "Avoid absolute terms (always, never)",
            "Make statements clearly true or false",
            "Test important concepts, not trivial details"
          ]
        },
        accessibility: [
          "Use inclusive language",
          "Avoid cultural bias",
          "Consider different learning styles",
          "Provide clear instructions"
        ],
        qualityMetrics: {
          excellent: "90-100: Clear, well-structured, educationally sound",
          good: "80-89: Minor issues, generally well-written",
          fair: "70-79: Some clarity or structure issues",
          poor: "60-69: Multiple issues requiring revision",
          failing: "Below 60: Significant problems, needs major revision"
        }
      }

      return APIResponse.success(guidelines, 'Quality guidelines retrieved successfully')

    } catch (error: any) {
      console.error('Error fetching quality guidelines:', error)
      return APIResponse.error(
        error.message || 'Failed to fetch quality guidelines',
        500
      )
    }
  }
)
