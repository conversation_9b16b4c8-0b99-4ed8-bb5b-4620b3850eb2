"use client"

import { use<PERSON>tate, use<PERSON>ffe<PERSON> } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Brain, 
  Wand2, 
  FileText, 
  Upload, 
  Settings, 
  Zap,
  Target,
  Clock,
  Users,
  BookOpen,
  Sparkles,
  Loader2,
  CheckCircle,
  AlertTriangle,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "lucide-react"
import { toast } from "sonner"
import { motion, AnimatePresence } from "framer-motion"

interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  speed: 'fast' | 'medium' | 'slow'
  quality: 'high' | 'medium' | 'low'
  pricing: { input: number; output: number }
}

interface ContentAnalysis {
  topic: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  suggestedQuestionCount: number
  learningObjectives: string[]
  keyTopics: string[]
  targetAudience: string
  estimatedDuration: number
  tags: string[]
  prerequisites: string[]
  suggestedSubject?: string
  suggestedChapter?: string
  suggestedTopic?: string
}

interface AIQuizCreatorProps {
  onQuizGenerated: (quiz: any) => void
  onClose: () => void
}

export function AIQuizCreator({ onQuizGenerated, onClose }: AIQuizCreatorProps) {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  
  // Content input
  const [content, setContent] = useState("")
  const [files, setFiles] = useState<File[]>([])
  
  // Analysis results
  const [analysis, setAnalysis] = useState<ContentAnalysis | null>(null)
  
  // Requirements
  const [requirements, setRequirements] = useState({
    questionCount: 10,
    difficulty: 'MEDIUM' as 'EASY' | 'MEDIUM' | 'HARD',
    questionTypes: ['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER'],
    timeLimit: 30,
    targetAudience: '',
    learningObjectives: [] as string[],
    subject: '',
    chapter: '',
    topic: ''
  })
  
  // Preferences
  const [preferences, setPreferences] = useState({
    prioritizeQuality: true,
    prioritizeSpeed: false,
    includeExplanations: true,
    includeImages: true,
    generateTags: true
  })
  
  // AI Models
  const [availableModels, setAvailableModels] = useState<AIModel[]>([])
  const [modelConfig, setModelConfig] = useState({
    orchestratorModel: '',
    contentAnalyzer: '',
    questionGenerator: '',
    qualityEvaluator: ''
  })
  
  // Progress tracking
  const [progress, setProgress] = useState(0)
  const [currentTask, setCurrentTask] = useState('')

  useEffect(() => {
    fetchAvailableModels()
  }, [])

  const fetchAvailableModels = async () => {
    try {
      const response = await fetch('/api/ai/quiz-creation')
      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.data.allModels)
        setModelConfig(data.data.defaultConfig)

        // Show info about OpenAI configuration
        if (data.data.usingOpenAIOnly) {
          toast.success('Using optimized OpenAI configuration for all tasks')
        }
      }
    } catch (error) {
      console.error('Error fetching models:', error)
      toast.error('Failed to load AI models. Please ensure OpenAI API key is configured.')
    }
  }

  // Auto-fill subject/chapter/topic from analysis
  useEffect(() => {
    if (analysis && step === 2) {
      setRequirements(prev => ({
        ...prev,
        subject: analysis.suggestedSubject || prev.subject,
        chapter: analysis.suggestedChapter || prev.chapter,
        topic: analysis.suggestedTopic || prev.topic
      }))
    }
  }, [analysis, step])

  const analyzeContent = async () => {
    if (!content.trim() && files.length === 0) {
      toast.error('Please provide content or upload files')
      return
    }

    try {
      setAnalyzing(true)
      
      const fileContents = await Promise.all(
        files.map(async (file) => ({
          name: file.name,
          content: await file.text(),
          type: file.type
        }))
      )

      const response = await fetch('/api/ai/quiz-creation/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          files: fileContents,
          analyzerModel: modelConfig.contentAnalyzer
        })
      })

      if (response.ok) {
        const data = await response.json()
        setAnalysis(data.data.analysis)

        // Update requirements based on analysis
        setRequirements(prev => ({
          ...prev,
          questionCount: data.data.analysis.suggestedQuestionCount,
          difficulty: data.data.analysis.difficulty,
          targetAudience: data.data.analysis.targetAudience
        }))

        setStep(2)
        toast.success('Content analyzed successfully!')
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `Analysis failed with status ${response.status}`
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Analysis error:', error)
      toast.error('Failed to analyze content')
    } finally {
      setAnalyzing(false)
    }
  }

  const generateQuiz = async () => {
    try {
      setLoading(true)
      setProgress(0)
      setCurrentTask('Initializing AI agents...')

      const fileContents = await Promise.all(
        files.map(async (file) => ({
          name: file.name,
          content: await file.text(),
          type: file.type
        }))
      )

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev < 90) return prev + 10
          return prev
        })
      }, 2000)

      const taskUpdates = [
        'Analyzing content structure...',
        'Planning quiz architecture...',
        'Generating questions...',
        'Evaluating question quality...',
        'Finalizing quiz structure...'
      ]

      let taskIndex = 0
      const taskInterval = setInterval(() => {
        if (taskIndex < taskUpdates.length) {
          setCurrentTask(taskUpdates[taskIndex])
          taskIndex++
        }
      }, 3000)

      const response = await fetch('/api/ai/quiz-creation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          files: fileContents,
          requirements,
          preferences,
          modelConfig
        })
      })

      clearInterval(progressInterval)
      clearInterval(taskInterval)

      if (response.ok) {
        const data = await response.json()
        console.log('API Response:', data) // Debug log

        setProgress(100)
        setCurrentTask('Quiz generated successfully!')

        setTimeout(() => {
          // Validate the response structure
          if (data && data.data && data.data.quiz) {
            console.log('Quiz data:', data.data.quiz) // Debug log
            onQuizGenerated(data.data.quiz)
            toast.success('AI-powered quiz created successfully!')
          } else {
            console.error('Invalid response structure:', data)
            toast.error('Invalid response from AI service')
          }
        }, 1000)
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `Quiz generation failed with status ${response.status}`
        console.error('API Error:', errorMessage, errorData)
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Generation error:', error)
      toast.error('Failed to generate quiz')
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(event.target.files || [])
    setFiles(prev => [...prev, ...uploadedFiles])
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const getModelBadgeColor = (speed: string, quality: string) => {
    if (quality === 'high') return 'bg-green-100 text-green-800'
    if (speed === 'fast') return 'bg-blue-100 text-blue-800'
    return 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="border-b p-6 bg-gradient-to-r from-purple-500 to-blue-600 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Brain className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">AI Quiz Creator</h2>
                <p className="text-purple-100">Create intelligent quizzes with advanced AI</p>
              </div>
            </div>
            <Button variant="ghost" onClick={onClose} className="text-white hover:bg-white/20">
              ×
            </Button>
          </div>
          
          {/* Progress Steps */}
          <div className="flex items-center gap-4 mt-6">
            {[
              { num: 1, label: 'Content', icon: FileText },
              { num: 2, label: 'Configure', icon: Settings },
              { num: 3, label: 'Generate', icon: Sparkles }
            ].map(({ num, label }) => (
              <div key={num} className="flex items-center gap-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= num ? 'bg-white text-purple-600' : 'bg-white/20 text-white'
                }`}>
                  {step > num ? <CheckCircle className="h-4 w-4" /> : num}
                </div>
                <span className="text-sm font-medium">{label}</span>
                {num < 3 && <div className="w-8 h-0.5 bg-white/30" />}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">Provide Your Content</h3>
                  <p className="text-muted-foreground">
                    Upload files or paste content that you want to create a quiz from
                  </p>
                </div>

                <Tabs defaultValue="text" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="text">Text Content</TabsTrigger>
                    <TabsTrigger value="files">File Upload</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="text" className="space-y-4">
                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        placeholder="Paste your content here... (articles, notes, textbook chapters, etc.)"
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        className="min-h-[200px] mt-2"
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="files" className="space-y-4">
                    <div>
                      <Label htmlFor="files">Upload Files</Label>
                      <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-600 mb-2">
                          Drop files here or click to upload
                        </p>
                        <input
                          type="file"
                          multiple
                          accept=".txt,.pdf,.doc,.docx,.md"
                          onChange={handleFileUpload}
                          className="hidden"
                          id="file-upload"
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('file-upload')?.click()}
                        >
                          Choose Files
                        </Button>
                      </div>
                      
                      {files.length > 0 && (
                        <div className="space-y-2">
                          {files.map((file, index) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">{file.name}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(index)}
                              >
                                ×
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={analyzeContent} 
                    disabled={analyzing || (!content.trim() && files.length === 0)}
                  >
                    {analyzing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Brain className="h-4 w-4 mr-2" />
                        Analyze Content
                      </>
                    )}
                  </Button>
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">Configure Your Quiz</h3>
                  <p className="text-muted-foreground">
                    Customize the quiz settings and AI model preferences
                  </p>

                  {/* OpenAI Configuration Info */}
                  <div className="mt-4 max-w-2xl mx-auto">
                    <Alert>
                      <Brain className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Optimized for OpenAI:</strong> This system uses GPT-4o for high-quality question generation and GPT-4o Mini for fast content analysis, providing the best balance of quality and speed.
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Analysis Results */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Content Analysis
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {analysis && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium">Topic</Label>
                              <p className="text-sm text-muted-foreground">{analysis.topic}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium">Difficulty</Label>
                              <Badge className={
                                analysis.difficulty === 'EASY' ? 'bg-green-100 text-green-800' :
                                analysis.difficulty === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }>
                                {analysis.difficulty}
                              </Badge>
                            </div>
                          </div>

                          <div>
                            <Label className="text-sm font-medium">Key Topics</Label>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {analysis.keyTopics.slice(0, 5).map((topic, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {topic}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div>
                            <Label className="text-sm font-medium">Learning Objectives</Label>
                            <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                              {analysis.learningObjectives.slice(0, 3).map((obj, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <Target className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                  {obj}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>

                  {/* Quiz Requirements */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        Quiz Settings
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Number of Questions</Label>
                        <div className="mt-2">
                          <Slider
                            value={[requirements.questionCount]}
                            onValueChange={(value) => setRequirements(prev => ({ ...prev, questionCount: value[0] }))}
                            max={50}
                            min={5}
                            step={1}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>5</span>
                            <span className="font-medium">{requirements.questionCount}</span>
                            <span>50</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <Label>Difficulty Level</Label>
                        <Select
                          value={requirements.difficulty}
                          onValueChange={(value: 'EASY' | 'MEDIUM' | 'HARD') =>
                            setRequirements(prev => ({ ...prev, difficulty: value }))
                          }
                        >
                          <SelectTrigger className="mt-2">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="EASY">Easy</SelectItem>
                            <SelectItem value="MEDIUM">Medium</SelectItem>
                            <SelectItem value="HARD">Hard</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Time Limit (minutes)</Label>
                        <Input
                          type="number"
                          value={requirements.timeLimit}
                          onChange={(e) => setRequirements(prev => ({
                            ...prev,
                            timeLimit: parseInt(e.target.value) || 30
                          }))}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Target Audience</Label>
                        <Input
                          value={requirements.targetAudience}
                          onChange={(e) => setRequirements(prev => ({
                            ...prev,
                            targetAudience: e.target.value
                          }))}
                          placeholder="e.g., High school students, Professionals"
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Question Types</Label>
                        <div className="mt-2 space-y-2">
                          {[
                            { value: 'MCQ', label: 'Multiple Choice' },
                            { value: 'TRUE_FALSE', label: 'True/False' },
                            { value: 'SHORT_ANSWER', label: 'Short Answer' },
                            { value: 'MATCHING', label: 'Matching' }
                          ].map(({ value, label }) => (
                            <div key={value} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={value}
                                checked={requirements.questionTypes.includes(value)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setRequirements(prev => ({
                                      ...prev,
                                      questionTypes: [...prev.questionTypes, value]
                                    }))
                                  } else {
                                    setRequirements(prev => ({
                                      ...prev,
                                      questionTypes: prev.questionTypes.filter(t => t !== value)
                                    }))
                                  }
                                }}
                                className="rounded border-gray-300"
                              />
                              <Label htmlFor={value} className="text-sm">{label}</Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Subject/Chapter/Topic Configuration */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        Content Organization
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Subject</Label>
                        <Input
                          value={requirements.subject}
                          onChange={(e) => setRequirements(prev => ({
                            ...prev,
                            subject: e.target.value
                          }))}
                          placeholder="e.g., Mathematics, Biology, History"
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Chapter</Label>
                        <Input
                          value={requirements.chapter}
                          onChange={(e) => setRequirements(prev => ({
                            ...prev,
                            chapter: e.target.value
                          }))}
                          placeholder="e.g., Algebra, Cell Biology, World War II"
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Topic</Label>
                        <Input
                          value={requirements.topic}
                          onChange={(e) => setRequirements(prev => ({
                            ...prev,
                            topic: e.target.value
                          }))}
                          placeholder="e.g., Linear Equations, Mitosis, Causes of WWI"
                          className="mt-2"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* AI Model Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Cpu className="h-5 w-5" />
                      AI Model Configuration
                    </CardTitle>
                    <CardDescription>
                      Choose different AI models for different tasks to optimize quality and speed
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {[
                        { key: 'orchestratorModel', label: 'Orchestrator', description: 'Plans and coordinates the quiz creation' },
                        { key: 'contentAnalyzer', label: 'Content Analyzer', description: 'Analyzes and understands the content' },
                        { key: 'questionGenerator', label: 'Question Generator', description: 'Creates quiz questions' },
                        { key: 'qualityEvaluator', label: 'Quality Evaluator', description: 'Reviews and improves questions' }
                      ].map(({ key, label, description }) => (
                        <div key={key} className="space-y-2">
                          <Label className="text-sm font-medium">{label}</Label>
                          <Select
                            value={modelConfig[key as keyof typeof modelConfig]}
                            onValueChange={(value) => setModelConfig(prev => ({ ...prev, [key]: value }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {availableModels.map((model) => (
                                <SelectItem key={model.id} value={model.id}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{model.name}</span>
                                    <div className="flex gap-1 ml-2">
                                      <Badge className={getModelBadgeColor(model.speed, model.quality)}>
                                        {model.speed}
                                      </Badge>
                                      <Badge variant="outline">
                                        {model.provider}
                                      </Badge>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-muted-foreground">{description}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Generation Preferences
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {[
                        { key: 'prioritizeQuality', label: 'Prioritize Quality', description: 'Use higher quality models for better results' },
                        { key: 'prioritizeSpeed', label: 'Prioritize Speed', description: 'Use faster models for quicker generation' },
                        { key: 'includeExplanations', label: 'Include Explanations', description: 'Generate detailed explanations for answers' },
                        { key: 'includeImages', label: 'Generate Images', description: 'AI will generate relevant images for questions' },
                        { key: 'generateTags', label: 'Auto-Generate Tags', description: 'Automatically create relevant tags for the quiz' }
                      ].map(({ key, label, description }) => (
                        <div key={key} className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-sm font-medium">{label}</Label>
                            <p className="text-xs text-muted-foreground">{description}</p>
                          </div>
                          <Switch
                            checked={preferences[key as keyof typeof preferences]}
                            onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, [key]: checked }))}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(1)}>
                    Back
                  </Button>
                  <Button onClick={() => setStep(3)}>
                    Continue to Generation
                  </Button>
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">Generate Your Quiz</h3>
                  <p className="text-muted-foreground">
                    AI agents are working together to create your perfect quiz
                  </p>
                </div>

                {loading ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center space-y-4">
                        <div className="flex justify-center">
                          <div className="relative">
                            <div className="w-16 h-16 border-4 border-purple-200 rounded-full animate-spin border-t-purple-600"></div>
                            <Brain className="w-8 h-8 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-purple-600" />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Progress value={progress} className="w-full max-w-md mx-auto" />
                          <p className="text-sm text-muted-foreground">{currentTask}</p>
                          <p className="text-xs text-muted-foreground">{progress}% complete</p>
                        </div>

                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            Multiple AI models are collaborating to create high-quality questions. This may take a few minutes.
                          </AlertDescription>
                        </Alert>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5" />
                        Ready to Generate
                      </CardTitle>
                      <CardDescription>
                        Review your settings and start the AI-powered quiz creation process
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                          <FileText className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                          <p className="font-medium">{requirements.questionCount} Questions</p>
                          <p className="text-sm text-muted-foreground">{requirements.difficulty} difficulty</p>
                        </div>
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <Clock className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                          <p className="font-medium">{requirements.timeLimit} Minutes</p>
                          <p className="text-sm text-muted-foreground">Time limit</p>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <Users className="h-8 w-8 mx-auto mb-2 text-green-600" />
                          <p className="font-medium">AI Agents</p>
                          <p className="text-sm text-muted-foreground">Multi-model approach</p>
                        </div>
                      </div>

                      <Alert>
                        <Sparkles className="h-4 w-4" />
                        <AlertDescription>
                          <strong>AI Models Selected:</strong> {Object.values(modelConfig).filter(Boolean).length} specialized models will work together to create your quiz.
                        </AlertDescription>
                      </Alert>
                    </CardContent>
                  </Card>
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(2)} disabled={loading}>
                    Back
                  </Button>
                  <Button onClick={generateQuiz} disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        Generate Quiz
                      </>
                    )}
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  )
}
