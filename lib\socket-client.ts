"use client"

import { io, Socket } from 'socket.io-client'
import { toastUtils } from './toast-utils'

interface SocketUser {
  id: string
  name: string
  email: string
  role: string
  socketId: string
  joinedAt: Date
  lastSeen: Date
}

interface NotificationData {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  userId?: string
  data?: any
  actionUrl?: string
  imageUrl?: string
  priority?: string
  category?: string
  createdAt: Date
  read?: boolean
  clicked?: boolean
  dismissed?: boolean
}

interface ChatMessage {
  id: string
  userId: string
  userName: string
  message: string
  type: 'text' | 'image' | 'file'
  timestamp: Date
  roomId: string
}

interface QuizProgress {
  userId: string
  questionIndex: number
  timeRemaining: number
  answered: boolean
}

class SocketClient {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  // Event listeners
  private eventListeners: Map<string, Function[]> = new Map()
  private recentNotifications: Set<string> = new Set() // Track recent notifications

  constructor() {
    if (typeof window !== 'undefined') {
      this.connect()
    }
  }

  private connect() {
    // Connect to socket server
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001'

    this.socket = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
      path: '/socket.io'
    })

    this.setupEventHandlers()
  }

  private getUserData() {
    // In a real implementation, you'd get this from your auth context or session
    // For now, we'll use localStorage or return mock data
    if (typeof window === 'undefined') return null

    try {
      const userData = localStorage.getItem('socket_user_data')
      return userData ? JSON.parse(userData) : null
    } catch {
      return null
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return

    // Connection events
    this.socket.on('connect', () => {
      console.log('🔌 Socket connected:', this.socket?.id)
      this.reconnectAttempts = 0
      this.emit('connection:established')
    })

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason)
      this.emit('connection:lost', reason)

      if (reason === 'io server disconnect') {
        this.handleReconnection()
      }
    })

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error)
      this.emit('connection:error', error)
      this.handleReconnection()
    })

    // Server events
    this.socket.on('connected', (data) => {
      console.log('✅ Connected to socket server:', data)
      this.emit('connection:success', data)
    })

    this.socket.on('authenticated', (data) => {
      console.log('✅ Authentication successful:', data)
      this.emit('authenticated', data)
    })

    this.socket.on('authentication_error', (error) => {
      console.error('❌ Authentication failed:', error)
      this.emit('authentication_error', error)
    })

    // User presence events
    this.socket.on('user:online', (data) => {
      this.emit('user:online', data)
    })

    this.socket.on('user:offline', (data) => {
      this.emit('user:offline', data)
    })

    // Notification events
    this.socket.on('notification', (notification: NotificationData) => {
      console.log('📨 Notification received:', notification)
      this.handleNotification(notification)
      this.emit('notification:received', notification)
    })

    this.socket.on('notification_sent', (data) => {
      console.log('✅ Notification sent:', data)
      this.emit('notification_sent', data)
    })

    // Quiz events
    this.socket.on('quiz:joined', (data) => {
      this.emit('quiz:joined', data)
    })

    this.socket.on('quiz:participant_joined', (data) => {
      this.emit('quiz:participant_joined', data)
    })

    this.socket.on('quiz:participant_left', (data) => {
      this.emit('quiz:participant_left', data)
    })

    this.socket.on('quiz:participant_progress', (data: QuizProgress) => {
      this.emit('quiz:participant_progress', data)
    })

    this.socket.on('quiz:participant_completed', (data) => {
      this.emit('quiz:participant_completed', data)
    })

    // Chat events
    this.socket.on('chat:user_joined', (data) => {
      this.emit('chat:user_joined', data)
    })

    this.socket.on('chat:message_received', (message: ChatMessage) => {
      console.log('📥 SocketClient: Received chat:message_received', message)
      this.emit('chat:message_received', message)
    })

    this.socket.on('chat:user_typing', (data) => {
      this.emit('chat:user_typing', data)
    })

    // Collaboration events
    this.socket.on('doc:user_joined', (data) => {
      this.emit('doc:user_joined', data)
    })

    this.socket.on('doc:changes_received', (data) => {
      this.emit('doc:changes_received', data)
    })

    this.socket.on('doc:cursor_moved', (data) => {
      this.emit('doc:cursor_moved', data)
    })
  }

  private handleNotification(notification: NotificationData) {
    // Create a unique key for deduplication
    const notificationKey = `${notification.title}-${notification.message}-${notification.type}`

    // Check if we've already shown this notification recently
    if (this.recentNotifications.has(notificationKey)) {
      return // Skip duplicate notification
    }

    // Add to recent notifications and remove after 5 seconds
    this.recentNotifications.add(notificationKey)
    setTimeout(() => {
      this.recentNotifications.delete(notificationKey)
    }, 5000)

    // Show toast notification using deduplicated utility
    switch (notification.type) {
      case 'success':
        toastUtils.success(notification.title, {
          description: notification.message
        })
        break
      case 'error':
        toastUtils.error(notification.title, {
          description: notification.message
        })
        break
      case 'warning':
        toastUtils.warning(notification.title, {
          description: notification.message
        })
        break
      default:
        toastUtils.default(notification.title, {
          description: notification.message
        })
    }
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('Socket server not available - real-time features disabled')
      this.emit('connection:failed')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`)

    setTimeout(() => {
      if (this.socket) {
        this.socket.connect()
      }
    }, delay)
  }

  // Public methods
  public setUserData(userData: {
    id: string
    name: string
    email: string
    role: string
    token: string
  }) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('socket_user_data', JSON.stringify(userData))
      
      // Reconnect with new user data
      if (this.socket) {
        this.socket.disconnect()
        this.connect()
      }
    }
  }

  public isConnected(): boolean {
    return this.socket?.connected || false
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  // Event listener management
  public on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  public off(event: string, callback?: Function) {
    if (!this.eventListeners.has(event)) return

    if (callback) {
      const listeners = this.eventListeners.get(event)!
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    } else {
      this.eventListeners.delete(event)
    }
  }

  private emit(event: string, data?: any) {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  // Quiz methods
  public joinQuiz(quizId: string) {
    this.socket?.emit('quiz:join', { quizId })
  }

  public leaveQuiz(quizId: string) {
    this.socket?.emit('quiz:leave', { quizId })
  }

  public updateQuizProgress(data: {
    quizId: string
    questionIndex: number
    timeRemaining: number
    answered: boolean
  }) {
    this.socket?.emit('quiz:progress', data)
  }

  public completeQuiz(data: {
    quizId: string
    score: number
    timeSpent: number
    rank?: number
  }) {
    this.socket?.emit('quiz:completed', data)
  }

  // Authentication method
  public authenticate(userData: {
    userId: string
    name: string
    email: string
    role: string
    token?: string
  }) {
    if (!this.socket) {
      console.error('Socket not connected')
      return
    }

    console.log('🔐 Authenticating user:', userData.name)
    this.socket.emit('authenticate', userData)
  }

  // Notification methods
  public sendNotification(data: {
    targetUserId?: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    data?: any
    broadcast?: boolean
  }) {
    this.socket?.emit('send_notification', data)
  }

  public broadcastNotification(data: {
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    data?: any
  }) {
    this.socket?.emit('send_notification', { ...data, broadcast: true })
  }

  // Quiz progress method
  public sendQuizProgress(data: {
    quizId: string
    questionIndex: number
    timeRemaining: number
    answered: boolean
  }) {
    this.socket?.emit('quiz_progress', data)
  }

  // Metrics methods
  public requestMetrics() {
    this.socket?.emit('get_metrics')
  }

  // Chat methods
  public joinChatRoom(roomId: string) {
    this.socket?.emit('chat:join', { roomId })
  }

  public leaveChatRoom(roomId: string) {
    this.socket?.emit('chat:leave', { roomId })
  }

  public sendChatMessage(data: {
    roomId: string
    message: string
    type?: 'text' | 'image' | 'file'
  }) {
    console.log('🚀 SocketClient: Emitting chat:message', data)
    this.socket?.emit('chat:message', data)
  }

  public setTyping(roomId: string, isTyping: boolean) {
    this.socket?.emit('chat:typing', { roomId, isTyping })
  }

  // Collaboration methods
  public joinDocument(documentId: string) {
    this.socket?.emit('doc:join', { documentId })
  }

  public sendDocumentChanges(data: {
    documentId: string
    changes: any
    version: number
  }) {
    this.socket?.emit('doc:change', data)
  }

  public updateCursor(data: {
    documentId: string
    position: { line: number, column: number }
  }) {
    this.socket?.emit('doc:cursor', data)
  }
}

// Singleton instance
let socketClient: SocketClient | null = null

export function getSocketClient(): SocketClient {
  if (!socketClient) {
    socketClient = new SocketClient()
  }
  return socketClient
}

export function initializeSocket(userData: {
  id: string
  name: string
  email: string
  role: string
  token?: string
}) {
  const client = getSocketClient()

  // Check if already initialized for this user
  const existingUserData = typeof window !== 'undefined'
    ? localStorage.getItem('socket_user_data')
    : null

  if (existingUserData) {
    const parsed = JSON.parse(existingUserData)
    if (parsed.id === userData.id && client.isConnected()) {
      console.log('🔌 Socket already initialized for user:', userData.name)
      // Still authenticate in case the previous authentication failed
      client.authenticate({
        userId: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        token: userData.token
      })
      return client
    }
  }

  console.log('🔌 Initializing socket for user:', userData.name)

  // Set user data for future use
  if (typeof window !== 'undefined') {
    localStorage.setItem('socket_user_data', JSON.stringify(userData))
  }

  // Authenticate with the server
  client.authenticate({
    userId: userData.id,
    name: userData.name,
    email: userData.email,
    role: userData.role,
    token: userData.token
  })

  return client
}

export type { SocketUser, NotificationData, ChatMessage, QuizProgress }
