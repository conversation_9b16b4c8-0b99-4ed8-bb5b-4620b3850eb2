"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Brain, 
  Settings, 
  Cpu, 
  Zap, 
  Target, 
  DollarSign,
  Save,
  RefreshCw,
  Info,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Sparkles
} from "lucide-react"
import { toast } from "sonner"
import { motion } from "framer-motion"
import { AIAgentDashboard } from "@/components/admin/ai-agent-dashboard"
import { AIModelSelector } from "@/components/admin/ai-model-selector"

interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  speed: 'fast' | 'medium' | 'slow'
  quality: 'high' | 'medium' | 'low'
  pricing: { input: number; output: number }
}

interface AISettings {
  defaultModels: {
    orchestrator: string
    contentAnalyzer: string
    questionGenerator: string
    qualityEvaluator: string
  }
  preferences: {
    prioritizeQuality: boolean
    prioritizeSpeed: boolean
    enableParallelProcessing: boolean
    maxRetries: number
    timeoutSeconds: number
    qualityThreshold: number
  }
  costLimits: {
    dailyLimit: number
    perQuizLimit: number
    alertThreshold: number
  }
  features: {
    enableAdvancedReasoning: boolean
    enableMultimodalInput: boolean
    enableRealTimeAnalysis: boolean
    enableCustomPrompts: boolean
  }
}

export default function AISettingsPage() {
  const [activeTab, setActiveTab] = useState("models")
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [availableModels, setAvailableModels] = useState<AIModel[]>([])
  const [settings, setSettings] = useState<AISettings>({
    defaultModels: {
      orchestrator: '',
      contentAnalyzer: '',
      questionGenerator: '',
      qualityEvaluator: ''
    },
    preferences: {
      prioritizeQuality: true,
      prioritizeSpeed: false,
      enableParallelProcessing: true,
      maxRetries: 3,
      timeoutSeconds: 120,
      qualityThreshold: 80
    },
    costLimits: {
      dailyLimit: 100,
      perQuizLimit: 5,
      alertThreshold: 80
    },
    features: {
      enableAdvancedReasoning: true,
      enableMultimodalInput: false,
      enableRealTimeAnalysis: true,
      enableCustomPrompts: false
    }
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/ai/quiz-creation')
      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.data.allModels)
        setSettings(prev => ({
          ...prev,
          defaultModels: data.data.defaultConfig
        }))
      }
    } catch (error) {
      console.error('Error fetching AI settings:', error)
      toast.error('Failed to load AI settings')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('AI settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const resetToDefaults = () => {
    setSettings({
      defaultModels: {
        orchestrator: 'gpt-4o',
        contentAnalyzer: 'claude-3-5-haiku-20241022',
        questionGenerator: 'gpt-4o',
        qualityEvaluator: 'claude-3-5-sonnet-20241022'
      },
      preferences: {
        prioritizeQuality: true,
        prioritizeSpeed: false,
        enableParallelProcessing: true,
        maxRetries: 3,
        timeoutSeconds: 120,
        qualityThreshold: 80
      },
      costLimits: {
        dailyLimit: 100,
        perQuizLimit: 5,
        alertThreshold: 80
      },
      features: {
        enableAdvancedReasoning: true,
        enableMultimodalInput: false,
        enableRealTimeAnalysis: true,
        enableCustomPrompts: false
      }
    })
    toast.success('Settings reset to defaults')
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Loading AI settings...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            AI Settings & Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Configure AI models, agents, and preferences for quiz creation
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Settings
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="models" className="flex items-center gap-2">
            <Cpu className="h-4 w-4" />
            Models
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="costs" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Cost Control
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Features
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
        </TabsList>

        <TabsContent value="models" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cpu className="h-5 w-5" />
                AI Model Configuration
              </CardTitle>
              <CardDescription>
                Select the best AI models for each specialized task in quiz creation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {[
                { 
                  key: 'orchestrator', 
                  title: 'Quiz Orchestrator', 
                  description: 'Plans and coordinates the entire quiz creation process',
                  icon: Brain
                },
                { 
                  key: 'contentAnalyzer', 
                  title: 'Content Analyzer', 
                  description: 'Analyzes and understands the input content',
                  icon: BarChart3
                },
                { 
                  key: 'questionGenerator', 
                  title: 'Question Generator', 
                  description: 'Creates high-quality quiz questions',
                  icon: Sparkles
                },
                { 
                  key: 'qualityEvaluator', 
                  title: 'Quality Evaluator', 
                  description: 'Reviews and improves question quality',
                  icon: Target
                }
              ].map(({ key, title, description, icon: Icon }) => (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-lg p-4"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Icon className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">{title}</h3>
                      <p className="text-sm text-muted-foreground">{description}</p>
                    </div>
                  </div>
                  
                  <AIModelSelector
                    models={availableModels}
                    selectedModel={settings.defaultModels[key as keyof typeof settings.defaultModels]}
                    onModelSelect={(modelId) => setSettings(prev => ({
                      ...prev,
                      defaultModels: { ...prev.defaultModels, [key]: modelId }
                    }))}
                    task={title}
                    taskDescription={description}
                  />
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dashboard" className="space-y-6">
          <AIAgentDashboard />
        </TabsContent>

        {/* Other tabs would be implemented similarly */}
        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Generation Preferences</CardTitle>
              <CardDescription>Configure how AI agents behave during quiz creation</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  These settings will be applied to all new quiz generation requests.
                </AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Prioritize Quality</Label>
                      <p className="text-xs text-muted-foreground">Use higher quality models</p>
                    </div>
                    <Switch
                      checked={settings.preferences.prioritizeQuality}
                      onCheckedChange={(checked) => setSettings(prev => ({
                        ...prev,
                        preferences: { ...prev.preferences, prioritizeQuality: checked }
                      }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Prioritize Speed</Label>
                      <p className="text-xs text-muted-foreground">Use faster models</p>
                    </div>
                    <Switch
                      checked={settings.preferences.prioritizeSpeed}
                      onCheckedChange={(checked) => setSettings(prev => ({
                        ...prev,
                        preferences: { ...prev.preferences, prioritizeSpeed: checked }
                      }))}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Quality Threshold (%)</Label>
                    <div className="mt-2">
                      <Slider
                        value={[settings.preferences.qualityThreshold]}
                        onValueChange={(value) => setSettings(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, qualityThreshold: value[0] }
                        }))}
                        max={100}
                        min={50}
                        step={5}
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>50%</span>
                        <span>{settings.preferences.qualityThreshold}%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
