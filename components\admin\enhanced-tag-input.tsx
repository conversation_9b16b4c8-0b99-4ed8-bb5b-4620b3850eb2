"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  X, 
  Wand2, 
  TrendingUp, 
  BookOpen, 
  Target, 
  Zap,
  Loader2,
  Sparkles
} from "lucide-react"
import { toast } from "sonner"
import { motion, AnimatePresence } from "framer-motion"

interface TagSuggestion {
  name: string
  category: 'subject' | 'topic' | 'difficulty' | 'skill' | 'format'
  relevance: number
  description: string
}

interface PopularTag {
  name: string
  count: number
}

interface EnhancedTagInputProps {
  tags: string[]
  onTagsChange: (tags: string[]) => void
  quizContent?: string
  placeholder?: string
  maxTags?: number
}

const categoryIcons = {
  subject: BookO<PERSON>,
  topic: Target,
  difficulty: TrendingUp,
  skill: Zap,
  format: Sparkles
}

const categoryColors = {
  subject: "bg-blue-100 text-blue-800 border-blue-200",
  topic: "bg-green-100 text-green-800 border-green-200", 
  difficulty: "bg-orange-100 text-orange-800 border-orange-200",
  skill: "bg-purple-100 text-purple-800 border-purple-200",
  format: "bg-pink-100 text-pink-800 border-pink-200"
}

export function EnhancedTagInput({ 
  tags, 
  onTagsChange, 
  quizContent = "",
  placeholder = "Add a tag",
  maxTags = 10
}: EnhancedTagInputProps) {
  const [newTag, setNewTag] = useState("")
  const [suggestions, setSuggestions] = useState<TagSuggestion[]>([])
  const [popularTags, setPopularTags] = useState<PopularTag[]>([])
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [loadingPopular, setLoadingPopular] = useState(false)

  useEffect(() => {
    fetchPopularTags()
  }, [])

  const fetchPopularTags = async () => {
    try {
      setLoadingPopular(true)
      const response = await fetch('/api/admin/quizzes/suggest-tags')
      if (response.ok) {
        const data = await response.json()
        setPopularTags(data.data.popularTags || [])
      }
    } catch (error) {
      console.error('Error fetching popular tags:', error)
    } finally {
      setLoadingPopular(false)
    }
  }

  const generateAISuggestions = async () => {
    if (!quizContent.trim()) {
      toast.error('Please add some quiz content first to generate AI suggestions')
      return
    }

    try {
      setLoadingSuggestions(true)
      const response = await fetch('/api/admin/quizzes/suggest-tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: quizContent,
          existingTags: tags
        })
      })

      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.data.suggestions || [])
        toast.success(`Generated ${data.data.suggestions?.length || 0} AI tag suggestions`)
      } else {
        throw new Error('Failed to generate suggestions')
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error)
      toast.error('Failed to generate AI tag suggestions')
    } finally {
      setLoadingSuggestions(false)
    }
  }

  const addTag = (tagName: string) => {
    const trimmedTag = tagName.trim()
    if (trimmedTag && !tags.includes(trimmedTag) && tags.length < maxTags) {
      onTagsChange([...tags, trimmedTag])
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag(newTag)
    }
  }

  const groupSuggestionsByCategory = () => {
    const grouped: Record<string, TagSuggestion[]> = {}
    suggestions.forEach(suggestion => {
      if (!grouped[suggestion.category]) {
        grouped[suggestion.category] = []
      }
      grouped[suggestion.category].push(suggestion)
    })
    return grouped
  }

  return (
    <div className="space-y-4">
      <div>
        <Label>Tags ({tags.length}/{maxTags})</Label>
        <div className="flex gap-2 mt-1">
          <Input
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder={placeholder}
            onKeyPress={handleKeyPress}
            disabled={tags.length >= maxTags}
          />
          <Button 
            type="button" 
            onClick={() => addTag(newTag)} 
            size="sm"
            disabled={!newTag.trim() || tags.length >= maxTags}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Current Tags */}
      {tags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <AnimatePresence>
            {tags.map((tag) => (
              <motion.div
                key={tag}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <Badge variant="secondary" className="flex items-center gap-1 pr-1">
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => removeTag(tag)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Tag Suggestions */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">Tag Suggestions</CardTitle>
              <CardDescription>
                Get AI-powered suggestions or choose from popular tags
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={generateAISuggestions}
              disabled={loadingSuggestions || !quizContent.trim()}
            >
              {loadingSuggestions ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Wand2 className="h-4 w-4 mr-2" />
              )}
              AI Suggest
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="popular" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="popular">Popular Tags</TabsTrigger>
              <TabsTrigger value="ai">AI Suggestions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="popular" className="space-y-3">
              {loadingPopular ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading popular tags...
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {popularTags
                    .filter(tag => !tags.includes(tag.name))
                    .slice(0, 15)
                    .map((tag) => (
                      <Button
                        key={tag.name}
                        variant="outline"
                        size="sm"
                        className="h-auto py-1 px-2 text-xs"
                        onClick={() => addTag(tag.name)}
                        disabled={tags.length >= maxTags}
                      >
                        {tag.name}
                        <Badge variant="secondary" className="ml-1 text-xs">
                          {tag.count}
                        </Badge>
                      </Button>
                    ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="ai" className="space-y-3">
              {suggestions.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <Sparkles className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Click "AI Suggest" to generate personalized tag recommendations</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {Object.entries(groupSuggestionsByCategory()).map(([category, categoryTags]) => {
                    const Icon = categoryIcons[category as keyof typeof categoryIcons]
                    return (
                      <div key={category}>
                        <div className="flex items-center gap-2 mb-2">
                          <Icon className="h-4 w-4" />
                          <span className="text-sm font-medium capitalize">{category}</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {categoryTags.map((suggestion) => (
                            <Button
                              key={suggestion.name}
                              variant="outline"
                              size="sm"
                              className={`h-auto py-1 px-2 text-xs ${categoryColors[suggestion.category]}`}
                              onClick={() => addTag(suggestion.name)}
                              disabled={tags.length >= maxTags}
                              title={suggestion.description}
                            >
                              {suggestion.name}
                              <Badge variant="secondary" className="ml-1 text-xs">
                                {Math.round(suggestion.relevance * 100)}%
                              </Badge>
                            </Button>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
